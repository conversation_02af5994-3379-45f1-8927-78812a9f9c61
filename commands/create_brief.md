Please create a product brief based on the description that the user provides. Your goal is to capture the business and functional requirements of the product and to provide solid context for others working on the product. You should include:

1. Project overview / description
2. Target audience
3. Primary benefits / features
4. High-level tech/architecture used

Keep the brief very concise and to the point just to give enough context to understand the bigger picture.
Write the document into docs/PRODUCT_BRIEF.md (unless a different file name is specified)