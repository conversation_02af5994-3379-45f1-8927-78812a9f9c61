{"name": "mcp-agent-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^2.0.15", "@ai-sdk/react": "^2.0.15", "@ai-sdk/rsc": "^1.0.15", "@icons-pack/react-simple-icons": "^13.7.0", "@langchain/anthropic": "^0.3.26", "@langchain/core": "^0.3.71", "@langchain/openai": "^0.6.8", "@modelcontextprotocol/server-filesystem": "^2025.8.18", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@radix-ui/react-use-controllable-state": "^1.2.2", "ai": "^5.0.15", "ai-elements": "^1.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "harden-react-markdown": "^1.0.4", "katex": "^0.16.22", "lucide-react": "^0.540.0", "mcp-use": "^0.1.17", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "tailwind-merge": "^3.3.1", "use-stick-to-bottom": "^1.1.1", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}